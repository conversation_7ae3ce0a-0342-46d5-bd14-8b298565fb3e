import type {
  scheduleHearingSchema,
  rescheduleHearingSchema,
  cancelHearingSchema,
} from './schemas';
import type { Prisma } from '@prisma/client';
import type { z } from 'zod';

export type ScheduleHearingData = z.infer<typeof scheduleHearingSchema>;
export type RescheduleHearingData = z.infer<typeof rescheduleHearingSchema>;
export type CancelHearingData = z.infer<typeof cancelHearingSchema>;

export interface HearingFilter {
  startDate?: string;
  endDate?: string;
  operatorId?: string;
  caseType?: string;
  status?: string;
}

export type CaseWithHearing = Prisma.CaseGetPayload<{
  include: {
    debtor: {
      select: {
        id: true;
        name: true;
        email: true;
        phone: true;
      };
    };
    operator: {
      select: {
        id: true;
        name: true;
        email: true;
      };
    };
  };
}>;
