'use client';

import { Plus } from 'lucide-react';
import { useState } from 'react';

import { Button } from '@/components/ui/button';

import { CreateUserDialog } from './components/create-user-dialog';

import type { UIRole } from '@/features/user/types';

interface CreateUserButtonProps {
  roles: UIRole[];
  onUserCreated?: () => void;
}

export function CreateUserButton({
  roles,
  onUserCreated,
}: Readonly<CreateUserButtonProps>) {
  const [showDialog, setShowDialog] = useState(false);

  const handleUserCreated = () => {
    setShowDialog(false);
    onUserCreated?.();
  };

  return (
    <>
      <Button onClick={() => setShowDialog(true)}>
        <Plus className="mr-2 h-4 w-4" />
        Nuevo Usuario
      </Button>
      <CreateUserDialog
        open={showDialog}
        onOpenChange={setShowDialog}
        onUserCreated={handleUserCreated}
        roles={roles}
      />
    </>
  );
}
