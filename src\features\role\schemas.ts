import { z } from 'zod';

export const createRoleSchema = z.object({
  name: z.string().min(1, 'El nombre del rol es obligatorio'),
  description: z.string(),
  permissions: z
    .array(z.string())
    .min(1, 'Debe seleccionar al menos un permiso'),
  color: z.string(),
});

export const updateRoleSchema = z.object({
  id: z.string().min(1, 'El ID es requerido'),
  name: z.string().min(1, 'El nombre del rol es obligatorio').optional(),
  description: z.string().optional(),
  permissions: z
    .array(z.string())
    .min(1, 'Debe seleccionar al menos un permiso')
    .optional(),
  color: z.string().optional(),
});

export type CreateRoleData = z.infer<typeof createRoleSchema>;
export type UpdateRoleData = z.infer<typeof updateRoleSchema>;
