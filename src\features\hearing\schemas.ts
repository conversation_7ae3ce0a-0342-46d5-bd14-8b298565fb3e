import { z } from 'zod';

export const scheduleHearingSchema = z.object({
  caseId: z.string().min(1, 'El ID del caso es requerido'),
  hearingDate: z.string().min(1, 'La fecha de audiencia es requerida'),
  notes: z.string().optional(),
});

export const rescheduleHearingSchema = z.object({
  caseId: z.string().min(1, 'El ID del caso es requerido'),
  newHearingDate: z.string().min(1, 'La nueva fecha de audiencia es requerida'),
  reason: z.string().min(1, 'La razón es requerida'),
});

export const cancelHearingSchema = z.object({
  caseId: z.string().min(1, 'El ID del caso es requerido'),
  reason: z.string().min(1, 'La razón es requerida'),
});
