import { PrismaClient, Role, User, Debtor, Creditor } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  const roles = await createRoles();

  const users = await createUsers(roles);

  const creditors = await createCreditors();

  const debtors = await createDebtors();

  await createCases(debtors, users, creditors);
}

async function createRoles() {
  const adminRole = await prisma.role.upsert({
    where: { name: 'Administrador' },
    update: {},
    create: {
      name: 'Administrador',
      description: 'Acceso completo al sistema',
      permissions: ['all'],
      color: 'bg-red-100 text-red-800',
    },
  });

  const operatorRole = await prisma.role.upsert({
    where: { name: 'Operadora de Insolvencia' },
    update: {},
    create: {
      name: 'Operadora de Insolvencia',
      description: 'Operador especializado en procesos de insolvencia',
      permissions: [
        'case.read',
        'case.create',
        'case.update',
        'case.delete',
        'document.create',
        'document.read',
        'document.update',
        'hearing.manage',
      ],
      color: 'bg-purple-100 text-purple-800',
    },
  });

  const lawyerRole = await prisma.role.upsert({
    where: { name: 'Abogado' },
    update: {},
    create: {
      name: 'Abogado',
      description: 'Gestión de casos y audiencias',
      permissions: [
        'case.read',
        'case.create',
        'case.update',
        'document.create',
        'document.read',
      ],
      color: 'bg-blue-100 text-blue-800',
    },
  });

  const assistantRole = await prisma.role.upsert({
    where: { name: 'Asistente Legal' },
    update: {},
    create: {
      name: 'Asistente Legal',
      description: 'Apoyo en gestión legal y documental',
      permissions: ['document.read', 'document.create', 'case.read'],
      color: 'bg-green-100 text-green-800',
    },
  });

  const secretaryRole = await prisma.role.upsert({
    where: { name: 'Secretaria' },
    update: {},
    create: {
      name: 'Secretaria',
      description: 'Apoyo administrativo',
      permissions: ['document.read', 'case.read'],
      color: 'bg-yellow-100 text-yellow-800',
    },
  });

  return { adminRole, operatorRole, lawyerRole, assistantRole, secretaryRole };
}

interface Roles {
  adminRole: Role;
  lawyerRole: Role;
  assistantRole: Role;
  operatorRole: Role;
  secretaryRole: Role;
}

async function createUsers(roles: Roles) {
  const beatriz = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Beatriz Helena Malavera López',
      email: '<EMAIL>',
      status: 'Activo',
      lastLogin: new Date('2025-01-29T08:30:00'),
      professionalCard: '194.548',
      phone: '3101234567',
      address: 'Calle 123 #45-67, Bogotá',
      createdDate: new Date('2025-01-01'),
      roleId: roles.operatorRole.id,
    },
  });

  const maximiliano = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Maximiliano Jaramillo Pérez',
      email: '<EMAIL>',
      status: 'Activo',
      lastLogin: new Date('2025-01-29T07:45:00'),
      professionalCard: '156.789',
      phone: '3202345678',
      address: 'Carrera 45 #12-34, Bogotá',
      createdDate: new Date('2025-01-01'),
      roleId: roles.lawyerRole.id,
    },
  });

  const carlos = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Carlos Andrés Rodríguez',
      email: '<EMAIL>',
      status: 'Activo',
      lastLogin: new Date('2025-01-28T16:20:00'),
      professionalCard: 'N/A',
      phone: '3153456789',
      address: 'Avenida 34 #56-78, Bogotá',
      createdDate: new Date('2025-01-01'),
      roleId: roles.assistantRole.id,
    },
  });

  const maria = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'María Fernanda González',
      email: '<EMAIL>',
      status: 'Inactivo',
      lastLogin: new Date('2025-01-25T14:15:00'),
      professionalCard: 'N/A',
      phone: '3164567890',
      address: 'Calle 89 #12-34, Bogotá',
      createdDate: new Date('2025-01-01'),
      roleId: roles.secretaryRole.id,
    },
  });

  return { beatriz, maximiliano, carlos, maria };
}

interface Debtors {
  maria: Debtor;
  carlos: Debtor;
  ana: Debtor;
}

interface Users {
  beatriz: User;
  maximiliano: User;
  carlos: User;
  maria: User;
}

interface Creditors {
  bancolombia: Creditor;
  coopvalle: Creditor;
  tarjetasxyz: Creditor;
  finpopular: Creditor;
}

async function createCreditors() {
  const bancolombia = await prisma.creditor.upsert({
    where: { nit: '860.002.964-4' },
    update: {},
    create: {
      name: 'Banco Nacional de Colombia',
      type: 'Entidad Financiera',
      email: '<EMAIL>',
      phone: '+57 1 234-5678',
      address: 'Carrera 7 #32-16, Bogotá',
      status: 'Activo',
      representative: 'María García',
      nit: '860.002.964-4',
      city: 'Bogotá',
      department: 'Cundinamarca',
      activeCases: 12,
      createdDate: new Date(),
      lastUpdate: new Date(),
    },
  });

  const coopvalle = await prisma.creditor.upsert({
    where: { nit: '890.300.567-8' },
    update: {},
    create: {
      name: 'Cooperativa Financiera del Valle',
      type: 'Cooperativa',
      email: '<EMAIL>',
      phone: '+57 2 345-6789',
      address: 'Avenida 6N #25-45, Cali',
      status: 'Activo',
      representative: 'Carlos Rodríguez',
      nit: '890.300.567-8',
      city: 'Cali',
      department: 'Valle del Cauca',
      activeCases: 8,
      createdDate: new Date(),
      lastUpdate: new Date(),
    },
  });

  const tarjetasxyz = await prisma.creditor.upsert({
    where: { nit: '900.123.456-7' },
    update: {},
    create: {
      name: 'Tarjetas de Crédito XYZ',
      type: 'Entidad Financiera',
      email: '<EMAIL>',
      phone: '+57 4 456-7890',
      address: 'Calle 50 #45-30, Medellín',
      status: 'Activo',
      representative: 'Ana Martínez',
      nit: '900.123.456-7',
      city: 'Medellín',
      department: 'Antioquia',
      activeCases: 15,
      createdDate: new Date(),
      lastUpdate: new Date(),
    },
  });

  const finpopular = await prisma.creditor.upsert({
    where: { nit: '800.456.789-1' },
    update: {},
    create: {
      name: 'Financiera Popular',
      type: 'Financiera',
      email: '<EMAIL>',
      phone: '+57 5 567-8901',
      address: 'Carrera 54 #72-15, Barranquilla',
      status: 'Inactivo',
      representative: 'Luis Fernández',
      nit: '800.456.789-1',
      city: 'Barranquilla',
      department: 'Atlántico',
      activeCases: 6,
      createdDate: new Date(),
      lastUpdate: new Date(),
    },
  });

  return { bancolombia, coopvalle, tarjetasxyz, finpopular };
}

async function createDebtors() {
  const maria = await prisma.debtor.upsert({
    where: { idNumber: '52123456' },
    update: {},
    create: {
      name: 'María González Pérez',
      idNumber: '52123456',
      idType: 'CC',
      email: '<EMAIL>',
      phone: '+57 ************',
      address: 'Calle 45 #12-34, Bogotá',
      city: 'Bogotá',
      department: 'Cundinamarca',
      birthDate: new Date('1985-05-15'),
      maritalStatus: 'CASADO',
      occupation: 'Empleada',
      monthlyIncome: 2500000,
      monthlyExpenses: 2200000,
      dependents: 2,
      educationLevel: 'UNIVERSITARIO',
      totalDebt: ********,
      status: 'En proceso',
      emergencyContact: 'Pedro González',
      emergencyPhone: '**********',
      bankAccount: '********',
      bankName: 'Bancolombia',
      accountType: 'AHORROS',
      activeCases: 1,
      createdDate: new Date(),
      lastUpdate: new Date(),
    },
  });

  const carlos = await prisma.debtor.upsert({
    where: { idNumber: '********' },
    update: {},
    create: {
      name: 'Carlos Rodríguez Silva',
      idNumber: '********',
      idType: 'CC',
      email: '<EMAIL>',
      phone: '+57 ************',
      address: 'Carrera 15 #67-89, Medellín',
      city: 'Medellín',
      department: 'Antioquia',
      birthDate: new Date('1978-08-20'),
      maritalStatus: 'SOLTERO',
      occupation: 'Independiente',
      monthlyIncome: 1800000,
      monthlyExpenses: 1600000,
      dependents: 0,
      educationLevel: 'POSTGRADO',
      totalDebt: ********,
      status: 'Audiencia programada',
      emergencyContact: 'Ana Rodríguez',
      emergencyPhone: '**********',
      bankAccount: '********',
      bankName: 'Davivienda',
      accountType: 'CORRIENTE',
      activeCases: 1,
      createdDate: new Date(),
      lastUpdate: new Date(),
    },
  });

  const ana = await prisma.debtor.upsert({
    where: { idNumber: '********' },
    update: {},
    create: {
      name: 'Ana Martínez López',
      idNumber: '********',
      idType: 'CC',
      email: '<EMAIL>',
      phone: '+57 ************',
      address: 'Avenida 80 #45-12, Cali',
      city: 'Cali',
      department: 'Valle del Cauca',
      birthDate: new Date('1990-03-10'),
      maritalStatus: 'UNION_LIBRE',
      occupation: 'Pensionada',
      monthlyIncome: 1200000,
      monthlyExpenses: 1000000,
      dependents: 1,
      educationLevel: 'TECNICO',
      totalDebt: ********,
      status: 'Documentos pendientes',
      bankAccount: '********',
      bankName: 'Banco de Bogotá',
      accountType: 'AHORROS',
      activeCases: 1,
      createdDate: new Date(),
      lastUpdate: new Date(),
    },
  });

  return { maria, carlos, ana };
}

async function createActivityLogs(users: Users) {
  await prisma.activityLog.createMany({
    data: [
      {
        date: new Date('2025-01-29T08:30:00'),
        action: 'Generó Auto de Admisión',
        userId: users.beatriz.id,
      },
      {
        date: new Date('2025-01-29T07:45:00'),
        action: 'Consultó reporte financiero',
        userId: users.maximiliano.id,
      },
      {
        date: new Date('2025-01-28T16:20:00'),
        action: 'Subió documento REDAM',
        userId: users.carlos.id,
      },
      {
        date: new Date('2025-01-25T14:15:00'),
        action: 'Inició sesión',
        userId: users.maria.id,
      },
    ],
  });
}

async function createCases(
  debtors: Debtors,
  users: Users,
  creditors: Creditors,
) {
  const case1 = await prisma.case.create({
    data: {
      caseNumber: 'INS-2025-001',
      debtorName: 'María González Pérez',
      type: 'INSOLVENCY',
      status: 'NEGOTIATION',
      totalDebt: ********,
      creditors: 5,
      createdDate: new Date('2025-01-15'),
      hearingDate: new Date('2025-02-14'),
      phase: 'En negociación',
      debtorId: debtors.maria.id,
      operatorId: users.beatriz.id,
    },
  });

  const case2 = await prisma.case.create({
    data: {
      caseNumber: 'CON-2025-002',
      debtorName: 'Carlos Rodríguez Silva',
      type: 'CONCILIATION',
      status: 'HEARING_SCHEDULED',
      totalDebt: ********,
      creditors: 3,
      createdDate: new Date('2025-01-10'),
      hearingDate: new Date('2025-02-09'),
      phase: 'Audiencia programada',
      debtorId: debtors.carlos.id,
      operatorId: users.maximiliano.id,
    },
  });

  const case3 = await prisma.case.create({
    data: {
      caseNumber: 'ACU-2025-003',
      debtorName: 'Ana Martínez López',
      type: 'SUPPORT_AGREEMENT',
      status: 'PENDING_DOCUMENTS',
      totalDebt: ********,
      creditors: 2,
      createdDate: new Date('2025-01-05'),
      hearingDate: null,
      phase: 'Documentos pendientes',
      debtorId: debtors.ana.id,
      operatorId: users.beatriz.id,
    },
  });

  const luis = await prisma.debtor.create({
    data: {
      name: 'Luis Fernando Castro',
      idNumber: '19876543',
      idType: 'CC',
      email: '<EMAIL>',
      phone: '+57 ************',
      address: 'Carrera 30 #25-90, Bogotá',
      city: 'Bogotá',
      department: 'Cundinamarca',
      birthDate: new Date('1975-12-01'),
      maritalStatus: 'CASADO',
      occupation: 'Empresario',
      monthlyIncome: 5000000,
      monthlyExpenses: 3500000,
      dependents: 3,
      educationLevel: 'POSTGRADO',
      totalDebt: ********,
      status: 'Acuerdo aprobado',
      emergencyContact: 'Carmen Castro',
      emergencyPhone: '**********',
      bankAccount: '********',
      bankName: 'BBVA',
      accountType: 'CORRIENTE',
      activeCases: 1,
      createdDate: new Date(),
      lastUpdate: new Date(),
    },
  });

  const case4 = await prisma.case.create({
    data: {
      caseNumber: 'INS-2025-004',
      debtorName: 'Luis Fernando Castro',
      type: 'INSOLVENCY',
      status: 'AGREEMENT_APPROVED',
      totalDebt: ********,
      creditors: 8,
      createdDate: new Date('2025-01-01'),
      hearingDate: new Date('2025-01-24'),
      phase: 'Acuerdo aprobado',
      debtorId: luis.id,
      operatorId: users.beatriz.id,
    },
  });

  const cases = [case1, case2, case3, case4];
  const creditorsList = [
    creditors.bancolombia,
    creditors.coopvalle,
    creditors.tarjetasxyz,
    creditors.finpopular,
  ];

  for (const caseItem of cases) {
    const numDebts = Math.min(caseItem.creditors, 3);
    for (let i = 0; i < numDebts; i++) {
      const creditor = creditorsList[i % creditorsList.length];
      const debtAmount = Math.floor(
        caseItem.totalDebt.toNumber() / caseItem.creditors,
      );

      await prisma.debt.create({
        data: {
          amount: debtAmount + i * 1000000,
          interestRate: 1.5 + i * 0.5,
          type: 'PERSONAL',
          caseId: caseItem.id,
          creditorId: creditor.id,
          debtorId: caseItem.debtorId,
        },
      });
    }

    const assetValueBase = 80000000;
    const assetValueVariation = cases.indexOf(caseItem) * 10000000;
    await prisma.asset.create({
      data: {
        name: 'Bien Inmueble',
        type: 'INMUEBLE',
        value: assetValueBase + assetValueVariation,
        caseId: caseItem.id,
        debtorId: caseItem.debtorId,
      },
    });

    let documentTypeName: string;
    if (caseItem.type === 'INSOLVENCY') {
      documentTypeName = 'Insolvencia';
    } else if (caseItem.type === 'CONCILIATION') {
      documentTypeName = 'Conciliación';
    } else {
      documentTypeName = 'Acuerdo';
    }

    let caseNumberLower: string;
    const caseIndex = cases.indexOf(caseItem) + 1;
    if (caseItem.type === 'INSOLVENCY') {
      caseNumberLower = `ins-2025-00${caseIndex}`;
    } else if (caseItem.type === 'CONCILIATION') {
      caseNumberLower = 'con-2025-002';
    } else {
      caseNumberLower = `acu-2025-00${caseIndex}`;
    }

    await prisma.document.create({
      data: {
        name: `Solicitud de ${documentTypeName}`,
        type: 'LAWSUIT',
        status:
          caseItem.status === 'AGREEMENT_APPROVED' ? 'APPROVED' : 'PENDING',
        uploadDate: caseItem.createdDate,
        url: `/documents/${caseNumberLower}_solicitud.pdf`,
        caseId: caseItem.id,
      },
    });
  }

  await createActivityLogs(users);
}

export { main };

const isMainModule =
  process.argv[1]?.endsWith('seed.ts') || process.argv[1]?.endsWith('seed.js');

if (isMainModule) {
  main()
    .catch((e) => {
      console.error(e);
      process.exit(1);
    })
    .finally(() => {
      prisma.$disconnect().catch((e) => {
        console.error('Error disconnecting from database:', e);
      });
    });
}
