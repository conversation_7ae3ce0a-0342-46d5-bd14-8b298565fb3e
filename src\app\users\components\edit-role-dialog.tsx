'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect, useTransition } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { updateRole } from '@/features/role/actions';
import { createRoleSchema } from '@/features/role/schemas';
import {
  PERMISSIONS,
  getPermissionNames,
  getPermissionKeys,
} from '@/lib/permissions';

import type { CreateRoleData } from '@/features/role/schemas';
import type { UIRole } from '@/features/user/types';

type RoleFormData = CreateRoleData;

interface EditRoleDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  role: UIRole | null;
  onRoleUpdated?: () => void;
}

export function EditRoleDialog({
  open,
  onOpenChange,
  role,
  onRoleUpdated,
}: Readonly<EditRoleDialogProps>) {
  const [isPending, startTransition] = useTransition();

  const form = useForm<RoleFormData>({
    resolver: zodResolver(createRoleSchema),
    defaultValues: {
      name: '',
      description: '',
      permissions: [],
      color: 'bg-gray-100 text-gray-800',
    },
  });

  useEffect(() => {
    if (role) {
      const permissionNames = getPermissionNames(role.permissions);
      form.reset({
        name: role.name,
        description: role.description,
        permissions: permissionNames,
        color: role.color,
      });
    }
  }, [role, form]);

  const availablePermissions = Object.values(PERMISSIONS).filter(
    (p) => p !== 'Acceso completo',
  );

  const colorOptions = [
    { value: 'bg-red-100 text-red-800', label: 'Rojo', preview: 'bg-red-100' },
    {
      value: 'bg-blue-100 text-blue-800',
      label: 'Azul',
      preview: 'bg-blue-100',
    },
    {
      value: 'bg-green-100 text-green-800',
      label: 'Verde',
      preview: 'bg-green-100',
    },
    {
      value: 'bg-yellow-100 text-yellow-800',
      label: 'Amarillo',
      preview: 'bg-yellow-100',
    },
    {
      value: 'bg-purple-100 text-purple-800',
      label: 'Morado',
      preview: 'bg-purple-100',
    },
    {
      value: 'bg-gray-100 text-gray-800',
      label: 'Gris',
      preview: 'bg-gray-100',
    },
  ];

  const onSubmit = (data: RoleFormData) => {
    if (!role) return;

    startTransition(async () => {
      const permissionKeys = getPermissionKeys(data.permissions);

      const { success, message } = await updateRole(role.id, {
        name: data.name,
        description: data.description,
        permissions: permissionKeys,
        color: data.color,
      });

      if (success) {
        toast.success(message);
        onOpenChange(false);
        onRoleUpdated?.();
      } else {
        toast.error(message);
      }
    });
  };

  if (!role) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-3xl">
        <DialogHeader>
          <DialogTitle>Editar Rol: {role.name}</DialogTitle>
          <DialogDescription>
            Modifique la configuración del rol seleccionado
          </DialogDescription>
        </DialogHeader>

        <form
          onSubmit={(e) => void form.handleSubmit(onSubmit)(e)}
          className="space-y-4"
        >
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="name">Nombre del Rol *</Label>
              <Input
                id="name"
                {...form.register('name')}
                className={form.formState.errors.name ? 'border-red-500' : ''}
              />
              {form.formState.errors.name && (
                <p className="text-sm text-red-500">
                  {form.formState.errors.name.message}
                </p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="color">Color del Badge</Label>
              <div className="flex space-x-2">
                {colorOptions.map((color) => (
                  <button
                    key={color.value}
                    type="button"
                    className={`h-8 w-8 rounded-full border-2 ${
                      color.preview
                    } ${
                      form.watch('color') === color.value
                        ? 'border-gray-800'
                        : 'border-gray-300'
                    }`}
                    onClick={() => form.setValue('color', color.value)}
                    title={color.label}
                  />
                ))}
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Descripción</Label>
            <Textarea id="description" {...form.register('description')} />
          </div>

          <div className="space-y-2">
            <Label>Permisos del Rol</Label>
            <div className="grid max-h-64 grid-cols-1 gap-2 overflow-y-auto rounded-lg border p-4 md:grid-cols-2">
              <Controller
                name="permissions"
                control={form.control}
                render={({ field }) => {
                  const handlePermissionChange = (
                    permission: string,
                    checked: boolean | 'indeterminate',
                  ) => {
                    const current = field.value || [];
                    if (checked === true) {
                      field.onChange([...current, permission]);
                    } else {
                      field.onChange(current.filter((p) => p !== permission));
                    }
                  };

                  return (
                    <>
                      {availablePermissions.map((permission) => (
                        <div
                          key={permission}
                          className="flex items-center space-x-2"
                        >
                          <Checkbox
                            id={permission}
                            checked={field.value?.includes(permission)}
                            onCheckedChange={(checked) =>
                              handlePermissionChange(permission, checked)
                            }
                          />
                          <Label
                            htmlFor={permission}
                            className="cursor-pointer text-sm"
                          >
                            {permission}
                          </Label>
                        </div>
                      ))}
                    </>
                  );
                }}
              />
            </div>
            {form.formState.errors.permissions && (
              <p className="text-sm text-red-500">
                {form.formState.errors.permissions.message}
              </p>
            )}
            <p className="text-sm text-gray-500">
              Seleccionados: {form.watch('permissions')?.length || 0} permisos
            </p>
          </div>

          <div className="rounded-lg border bg-blue-50 p-4">
            <h3 className="mb-2 font-medium">Información del Rol</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-gray-600">Usuarios con este rol:</p>
                <p className="font-medium">{role.users}</p>
              </div>
              <div>
                <p className="text-gray-600">Permisos actuales:</p>
                <p className="font-medium">{role.permissions.length}</p>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isPending}
            >
              Cancelar
            </Button>
            <Button type="submit" disabled={isPending}>
              {isPending ? 'Guardando...' : 'Guardar Cambios'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
