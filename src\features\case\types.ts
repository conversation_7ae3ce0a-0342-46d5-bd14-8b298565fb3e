import type { createCaseSchema, updateCaseSchema } from './schemas';
import type { Prisma } from '@prisma/client';
import type { z } from 'zod';

export interface CaseData {
  id: string;
  caseNumber: string;
  debtorName: string;
  type: string;
  status: string;
  totalDebt: number;
  creditors: number;
  createdDate: Date;
  hearingDate: Date | null;
  phase: string | null;
  causes: string[];
  debtorId: string;
  operatorId: string;
  debtor: {
    id: string;
    name: string;
    idNumber: string;
    email: string;
    phone: string;
  };
  operator: {
    id: string;
    name: string;
    email: string;
  };
  debts?: {
    id: string;
    amount: number;
    interestRate: number;
    type: string;
    caseId: string;
    creditorId: string;
    debtorId: string | null;
    creditor: {
      id: string;
      name: string;
      type: string;
      email: string;
      phone: string;
      address: string;
      status: string;
      representative: string;
      nit: string;
      website: string | null;
      city: string | null;
      department: string | null;
      bankName: string | null;
      activeCases: number;
      createdDate: Date | null;
      lastUpdate: Date | null;
      description: string | null;
      representativeId: string | null;
      representativeEmail: string | null;
      representativePhone: string | null;
    };
  }[];
  documents?: {
    id: string;
    name: string;
    type: string;
    status: string;
    uploadDate: Date;
    url: string;
    caseId: string;
  }[];
  assets?: {
    id: string;
    name: string;
    type: string;
    value: number;
    caseId: string;
    debtorId: string | null;
  }[];
  _count: {
    documents: number;
  };
}

export interface CaseStats {
  total: number;
  byStatus: {
    status: string;
    _count: {
      id: number;
    };
  }[];
  byType: {
    type: string;
    _count: {
      id: number;
    };
  }[];
  negotiation: number;
  agreementApproved: number;
  totalDebt: number;
}

export interface Asset {
  id: string;
  name: string;
  type: string;
  value: number;
}

export interface Debt {
  id: string;
  creditor: string;
  amount: number;
  interestRate: number;
  type: string;
}

export interface LegalProcess {
  id: string;
  description: string;
}

export type CaseWithRelations = Prisma.CaseGetPayload<{
  include: {
    debtor: true;
    operator: {
      select: {
        id: true;
        name: true;
      };
    };
    documents: true;
    debts: {
      include: {
        creditor: {
          select: {
            name: true;
          };
        };
      };
    };
    assets: true;
  };
}>;

export interface CaseActionResult {
  id: string;
  caseNumber: string;
  debtorName: string;
  type: string;
  status: string;
  totalDebt: number;
  creditors: number;
  createdDate: Date;
  hearingDate: Date | null;
  phase: string | null;
  causes: string[];
  debtorId: string;
  operatorId: string;
}

export type CreateCaseData = z.infer<typeof createCaseSchema>;
export type UpdateCaseData = z.infer<typeof updateCaseSchema>;
