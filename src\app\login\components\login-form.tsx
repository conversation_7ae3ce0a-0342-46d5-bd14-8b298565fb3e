'use client';

import { <PERSON>, EyeOff, Mail, Lock, ArrowLeft } from 'lucide-react';
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import { useState, useEffect } from 'react';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import type React from 'react';

interface DatabaseInfo {
  id: string;
  name: string;
  icon: string;
  color: string;
}

const databaseInfo: Record<string, DatabaseInfo> = {
  armonia: {
    id: 'armonia',
    name: 'Armonia',
    icon: '/images/toucan.png',
    color: 'text-orange-600',
  },
  'constructores-paz': {
    id: 'constructores-paz',
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON> de <PERSON>',
    icon: '/images/dove-of-peace.png',
    color: 'text-blue-600',
  },
};

export function LoginForm() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [selectedDb, setSelectedDb] = useState<string | null>(null);

  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    const dbParam = searchParams.get('db');
    const storedDb = localStorage.getItem('selectedDatabase');

    if (dbParam && databaseInfo[dbParam]) {
      setSelectedDb(dbParam);
      localStorage.setItem('selectedDatabase', dbParam);
    } else if (storedDb && databaseInfo[storedDb]) {
      setSelectedDb(storedDb);
    } else {
      router.push('/');
    }
  }, [searchParams, router]);

  const currentDb = selectedDb ? databaseInfo[selectedDb] : null;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));

      const validCredentials = [
        {
          email: '<EMAIL>',
          // eslint-disable-next-line sonarjs/no-hardcoded-passwords
          password: 'admin123',
          role: 'admin',
          name: 'Beatriz Helena Malavera',
        },
        {
          email: '<EMAIL>',
          // eslint-disable-next-line sonarjs/no-hardcoded-passwords
          password: 'abogado123',
          role: 'lawyer',
          name: 'Maximiliano Jaramillo',
        },
        {
          email: '<EMAIL>',
          // eslint-disable-next-line sonarjs/no-hardcoded-passwords
          password: 'admin123',
          role: 'admin',
          name: 'Administrador Armonia',
        },
        {
          email: '<EMAIL>',
          // eslint-disable-next-line sonarjs/no-hardcoded-passwords
          password: 'abogado123',
          role: 'lawyer',
          name: 'Abogado Armonia',
        },
      ];

      const user = validCredentials.find(
        (cred) => cred.email === email && cred.password === password,
      );

      if (user) {
        localStorage.setItem(
          'user',
          JSON.stringify({
            ...user,
            database: selectedDb,
          }),
        );
        router.push('/dashboard');
      } else {
        setError('Credenciales inválidas. Verifique su correo y contraseña.');
      }
    } catch {
      setError('Error al iniciar sesión');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToSelector = () => {
    localStorage.removeItem('selectedDatabase');
    router.push('/');
  };

  if (!currentDb) {
    return <div>Cargando...</div>;
  }

  return (
    <div className="w-full max-w-md">
      <div className="mb-8 text-center">
        <div className="mb-4 flex items-center justify-center">
          <Image
            src={currentDb.icon || '/placeholder.svg'}
            alt={`${currentDb.name} Icon`}
            width={60}
            height={60}
            className="mr-3"
          />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">INSOLVENTIC</h1>
            <Badge
              variant="outline"
              className={`${currentDb.color} mt-1 border-current`}
            >
              {currentDb.name}
            </Badge>
          </div>
        </div>
        <p className="text-gray-600">
          Sistema de Gestión de Procesos de Insolvencia
        </p>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Iniciar Sesión</CardTitle>
              <CardDescription>
                Acceso a la base de datos de {currentDb.name}
              </CardDescription>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBackToSelector}
              className="text-gray-500 hover:text-gray-700"
            >
              <ArrowLeft className="mr-1 h-4 w-4" />
              Cambiar
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <form onSubmit={(e) => void handleSubmit(e)} className="space-y-4">
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="space-y-2">
              <Label htmlFor="email">Correo Electrónico / Cédula</Label>
              <div className="relative">
                <Mail className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
                <Input
                  id="email"
                  type="text"
                  placeholder="<EMAIL> o 12345678"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="pl-10"
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">Contraseña</Label>
              <div className="relative">
                <Lock className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="••••••••"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="pr-10 pl-10"
                  required
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4 text-gray-400" />
                  ) : (
                    <Eye className="h-4 w-4 text-gray-400" />
                  )}
                </Button>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="remember"
                checked={rememberMe}
                onCheckedChange={(checked) => setRememberMe(checked === true)}
              />
              <Label htmlFor="remember" className="text-sm font-normal">
                Recordar usuario
              </Label>
            </div>

            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? 'Iniciando sesión...' : 'Iniciar Sesión'}
            </Button>

            <div className="text-center">
              <Button variant="link" className="text-sm">
                ¿Olvidó su contraseña?
              </Button>
            </div>
          </form>

          <div className="mt-6 rounded-lg bg-blue-50 p-4">
            <p className="mb-2 text-sm font-medium text-blue-800">
              Credenciales de prueba para {currentDb.name}:
            </p>
            {selectedDb === 'constructores-paz' ? (
              <>
                <p className="text-xs text-blue-700">
                  Admin: <EMAIL> / admin123
                </p>
                <p className="text-xs text-blue-700">
                  Abogado: <EMAIL> / abogado123
                </p>
              </>
            ) : (
              <>
                <p className="text-xs text-blue-700">
                  Admin: <EMAIL> / admin123
                </p>
                <p className="text-xs text-blue-700">
                  Abogado: <EMAIL> / abogado123
                </p>
              </>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
