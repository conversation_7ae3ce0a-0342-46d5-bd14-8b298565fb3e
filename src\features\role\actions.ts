'use server';

import { revalidatePath } from 'next/cache';

import prisma from '@/lib/prisma';

export async function getAllRoles() {
  return await prisma.role.findMany({
    include: {
      _count: {
        select: {
          users: true,
        },
      },
    },
  });
}

export async function createRole(data: {
  name: string;
  description: string;
  permissions: string[];
  color?: string;
}) {
  try {
    const newRole = await prisma.role.create({
      data: {
        name: data.name,
        description: data.description,
        permissions: data.permissions,
        color: data.color,
      },
      include: {
        users: true,
      },
    });

    revalidatePath('/users');

    return {
      success: true,
      message: 'Rol creado exitosamente',
      data: newRole,
    };
  } catch (error) {
    console.error('Error creating role:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

export async function updateRole(
  id: string,
  data: {
    name?: string;
    description?: string;
    permissions?: string[];
    color?: string;
  },
) {
  try {
    const updatedRole = await prisma.role.update({
      where: { id },
      data,
      include: {
        users: true,
      },
    });

    revalidatePath('/users');

    return {
      success: true,
      message: 'Rol actualizado exitosamente',
      data: updatedRole,
    };
  } catch (error) {
    console.error('Error updating role:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

export async function deleteRole(id: string) {
  try {
    const usersWithRole = await prisma.user.count({
      where: { roleId: id },
    });

    if (usersWithRole > 0) {
      return {
        success: false,
        message: 'No se puede eliminar el rol porque tiene usuarios asignados',
      };
    }

    await prisma.role.delete({
      where: { id },
    });

    revalidatePath('/users');

    return {
      success: true,
      message: 'Rol eliminado exitosamente',
    };
  } catch (error) {
    console.error('Error deleting role:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

export async function updateRolePermissions(
  roleId: string,
  permissions: string[],
) {
  try {
    const updatedRole = await prisma.role.update({
      where: { id: roleId },
      data: { permissions },
      include: {
        users: true,
      },
    });

    revalidatePath('/users');

    return {
      success: true,
      message: 'Permisos actualizados exitosamente',
      data: updatedRole,
    };
  } catch (error) {
    console.error('Error updating role permissions:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}
