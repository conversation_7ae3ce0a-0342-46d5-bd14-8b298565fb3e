'use client';

import {
  User,
  CreditCard,
  Mail,
  Phone,
  MapPin,
  DollarSign,
  UserPlus,
} from 'lucide-react';
import React from 'react';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { parseCurrencyInput } from '@/lib/utils';

interface DebtorOption {
  id: string;
  name: string;
  idNumber: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  department: string;
  monthlyIncome: number;
  monthlyExpenses: number;
}

interface DebtorSectionProps {
  debtors: DebtorOption[];
  selectedDebtorId: string;
  selectedDebtor: DebtorOption | null;
  formData: {
    caseType: string;
    totalDebt: string;
    causes: string[];
  };
  insolvencyCauses: string[];
  onDebtorSelect: (debtorId: string) => void;
  onCreateDebtorClick: () => void;
  onFormDataChange: (data: {
    caseType: string;
    totalDebt: string;
    causes: string[];
  }) => void;
  onCauseChange: (cause: string) => void;
  loading?: boolean;
}

export function DebtorSection({
  debtors,
  selectedDebtorId,
  selectedDebtor,
  formData,
  insolvencyCauses,
  onDebtorSelect,
  onCreateDebtorClick,
  onFormDataChange,
  onCauseChange,
  loading = false,
}: Readonly<DebtorSectionProps>) {
  const renderSelectContent = () => {
    if (loading) {
      return [
        <SelectItem key="loading" value="loading" disabled>
          Cargando deudores...
        </SelectItem>,
      ];
    }

    if (debtors.length === 0) {
      return [
        <SelectItem key="no-debtors" value="no-debtors" disabled>
          No hay deudores registrados
        </SelectItem>,
      ];
    }

    return debtors.map((debtor) => (
      <SelectItem key={debtor.id} value={debtor.id}>
        {debtor.name} - CC: {debtor.idNumber}
      </SelectItem>
    ));
  };

  return (
    <Card className="shadow-sm">
      <CardHeader>
        <CardTitle>Información del Deudor</CardTitle>
        <CardDescription>
          Datos personales y financieros del deudor
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="debtorSelect">Seleccionar Deudor</Label>
            <div className="flex gap-2">
              <Select
                value={selectedDebtorId}
                onValueChange={onDebtorSelect}
                disabled={loading}
              >
                <SelectTrigger className="flex-1">
                  <SelectValue
                    placeholder={
                      loading
                        ? 'Cargando deudores...'
                        : 'Seleccione un deudor existente'
                    }
                  />
                </SelectTrigger>
                <SelectContent>{renderSelectContent()}</SelectContent>
              </Select>
              <Button
                variant="outline"
                size="icon"
                onClick={onCreateDebtorClick}
                title="Crear nuevo deudor"
              >
                <UserPlus className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {selectedDebtor && (
            <Card className="bg-gray-50">
              <CardContent className="pt-6">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div className="flex items-start space-x-3">
                    <User className="mt-0.5 h-5 w-5 text-gray-500" />
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Nombre
                      </p>
                      <p className="text-base">{selectedDebtor.name}</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <CreditCard className="mt-0.5 h-5 w-5 text-gray-500" />
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Cédula
                      </p>
                      <p className="text-base">{selectedDebtor.idNumber}</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <Mail className="mt-0.5 h-5 w-5 text-gray-500" />
                    <div>
                      <p className="text-sm font-medium text-gray-600">Email</p>
                      <p className="text-base">{selectedDebtor.email}</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <Phone className="mt-0.5 h-5 w-5 text-gray-500" />
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Teléfono
                      </p>
                      <p className="text-base">{selectedDebtor.phone}</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3 md:col-span-2">
                    <MapPin className="mt-0.5 h-5 w-5 text-gray-500" />
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Dirección
                      </p>
                      <p className="text-base">
                        {selectedDebtor.address}, {selectedDebtor.city},{' '}
                        {selectedDebtor.department}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <DollarSign className="mt-0.5 h-5 w-5 text-green-600" />
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Ingresos Mensuales
                      </p>
                      <p className="text-base font-semibold text-green-600">
                        ${selectedDebtor.monthlyIncome.toLocaleString('es-CO')}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <DollarSign className="mt-0.5 h-5 w-5 text-red-600" />
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Gastos Mensuales
                      </p>
                      <p className="text-base font-semibold text-red-600">
                        $
                        {selectedDebtor.monthlyExpenses.toLocaleString('es-CO')}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="caseType">Tipo de Caso</Label>
            <Select
              value={formData.caseType}
              onValueChange={(value) =>
                onFormDataChange({ ...formData, caseType: value })
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Seleccionar tipo" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="INSOLVENCY">Insolvencia</SelectItem>
                <SelectItem value="CONCILIATION">Conciliación</SelectItem>
                <SelectItem value="SUPPORT_AGREEMENT">
                  Acuerdo de Apoyo
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="totalDebt">Deuda Total</Label>
            <div className="relative">
              <span className="absolute top-1/2 left-3 -translate-y-1/2 text-gray-500">
                $
              </span>
              <Input
                id="totalDebt"
                type="text"
                value={
                  formData.totalDebt
                    ? Number(formData.totalDebt).toLocaleString('es-CO')
                    : ''
                }
                onChange={(e) => {
                  const value = parseCurrencyInput(e.target.value);
                  onFormDataChange({
                    ...formData,
                    totalDebt: value,
                  });
                }}
                placeholder="0"
                className="pl-8"
              />
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <Label>Causas de la Insolvencia</Label>
          <div className="grid grid-cols-1 gap-2 md:grid-cols-2">
            {insolvencyCauses.map((cause, index) => (
              <div key={index} className="flex items-center space-x-2">
                <Checkbox
                  id={`cause-${index}`}
                  checked={formData.causes.includes(cause)}
                  onCheckedChange={() => onCauseChange(cause)}
                />
                <Label
                  htmlFor={`cause-${index}`}
                  className="text-sm font-normal"
                >
                  {cause}
                </Label>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
