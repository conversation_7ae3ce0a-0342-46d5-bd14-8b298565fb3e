import { getAllHearings, getHearingStats } from '@/features/hearing/actions';

import { DashboardHeader } from '../dashboard/components/dashboard-header';

import { HearingsPageClient } from './hearings-page-client';

export default async function HearingsPage() {
  const [hearingsData, statsData] = await Promise.all([
    getAllHearings(),
    getHearingStats(),
  ]);

  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardHeader />

      <main className="container mx-auto px-4 py-6">
        <HearingsPageClient
          initialHearings={hearingsData}
          initialStats={statsData}
        />
      </main>
    </div>
  );
}
