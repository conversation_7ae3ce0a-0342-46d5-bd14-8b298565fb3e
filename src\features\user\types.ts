import type {
  createUserSchema,
  updateUserSchema,
  editUserFormSchema,
  createUserFormSchema,
} from './schemas';
import type { User as PrismaUser, Role as PrismaRole } from '@prisma/client';
import type { z } from 'zod';

export type UserWithRole = PrismaUser & {
  role: PrismaRole;
  assignedCases: { id: string }[];
};

export type UIUser = Omit<UserWithRole, 'lastLogin' | 'createdDate'> & {
  lastLogin: string;
  createdDate: string;
  casesAssigned: number;
  permissions: string[];
};

export type UIRole = PrismaRole & {
  users: number;
};

export type CreateUserData = z.infer<typeof createUserSchema>;
export type UpdateUserData = z.infer<typeof updateUserSchema>;
export type EditUserFormData = z.infer<typeof editUserFormSchema>;
export type CreateUserFormData = z.infer<typeof createUserFormSchema>;
