'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import {
  Building,
  Phone,
  Mail,
  MapPin,
  User,
  FileText,
  Hash,
  Globe,
  Edit,
  Building2,
  Users,
  Plus,
  Trash2,
} from 'lucide-react';
import { useState, useEffect, useRef, useTransition } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import {
  updateCreditor,
  addContact,
  removeContact,
} from '@/features/creditor/actions';
import { updateCreditorFormSchema } from '@/features/creditor/schemas';

import type {
  UpdateCreditorFormData,
  CreateContactData,
  CreditorWithRelations,
} from '@/features/creditor/types';

interface EditCreditorDialogProps {
  creditor: CreditorWithRelations | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCreditorUpdated?: () => void;
}

export function EditCreditorDialog({
  creditor,
  open,
  onOpenChange,
  onCreditorUpdated,
}: Readonly<EditCreditorDialogProps>) {
  const [isPending, startTransition] = useTransition();
  const closeRef = useRef<HTMLButtonElement>(null);
  const [newContact, setNewContact] = useState<CreateContactData>({
    name: '',
    email: '',
    phone: '',
    role: '',
  });

  const form = useForm<UpdateCreditorFormData>({
    resolver: zodResolver(updateCreditorFormSchema),
    defaultValues: {
      name: '',
      nit: '',
      type: 'BANK',
      email: '',
      phone: '',
      address: '',
      city: '',
      department: '',
      representative: '',
      representativeEmail: '',
      representativePhone: '',
      website: '',
      description: '',
    },
  });

  useEffect(() => {
    if (creditor) {
      let creditorType: 'BANK' | 'COOPERATIVE' | 'OTHER' = 'OTHER';
      if (creditor.type === 'Entidad Financiera') {
        creditorType = 'BANK';
      } else if (creditor.type === 'Cooperativa') {
        creditorType = 'COOPERATIVE';
      }

      form.reset({
        name: creditor.name,
        nit: creditor.nit,
        type: creditorType,
        email: creditor.email,
        phone: creditor.phone,
        address: creditor.address,
        city: creditor.city ?? '',
        department: creditor.department ?? '',
        representative: creditor.representative,
        representativeEmail: creditor.representativeEmail ?? '',
        representativePhone: creditor.representativePhone ?? '',
        website: creditor.website ?? '',
        description: creditor.description ?? '',
      });
    }
  }, [creditor, form]);

  if (!creditor) return null;

  const handleSubmit = (data: UpdateCreditorFormData) => {
    startTransition(async () => {
      const { success, message } = await updateCreditor({
        ...data,
        id: creditor.id,
      });

      if (success) {
        toast.success(message);
        closeRef.current?.click();
        onCreditorUpdated?.();
      } else {
        toast.error(message);
      }
    });
  };

  const handleAddContact = () => {
    if (newContact.name && newContact.email && creditor) {
      startTransition(async () => {
        const { success, message } = await addContact(creditor.id, newContact);
        if (success) {
          toast.success(message);
          setNewContact({ name: '', email: '', phone: '', role: '' });
          onCreditorUpdated?.();
        } else {
          toast.error(message);
        }
      });
    }
  };

  const handleDeleteContact = (contactId: string) => {
    startTransition(async () => {
      const { success, message } = await removeContact(contactId);
      if (success) {
        toast.success(message);
        onCreditorUpdated?.();
      } else {
        toast.error(message);
      }
    });
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'BANK':
        return <Building className="h-4 w-4" />;
      case 'COOPERATIVE':
        return <Users className="h-4 w-4" />;
      default:
        return <Building2 className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    return status === 'Activo'
      ? 'bg-green-100 text-green-800 border-green-300'
      : 'bg-red-100 text-red-800 border-red-300';
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-6xl">
        <DialogHeader>
          <div className="flex items-center justify-between pr-8">
            <DialogTitle className="flex items-center gap-2">
              <Edit className="h-5 w-5" />
              Editar Acreedor
            </DialogTitle>
            <Badge className={getStatusColor(creditor.status)}>
              {creditor.status}
            </Badge>
          </div>
          <DialogDescription>
            Modifique la información del acreedor. Los campos marcados con * son
            obligatorios.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={(e) => void form.handleSubmit(handleSubmit)(e)}
            className="space-y-6"
          >
            <Tabs defaultValue="general" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="general">Información General</TabsTrigger>
                <TabsTrigger value="contact">Contacto</TabsTrigger>
                <TabsTrigger value="representative">
                  Representante Legal
                </TabsTrigger>
                <TabsTrigger value="contacts">Contactos</TabsTrigger>
              </TabsList>

              <TabsContent value="general" className="mt-4 space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-base">
                      <Building className="h-4 w-4" />
                      Datos de la Empresa
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                      <FormField
                        control={form.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="flex items-center gap-2">
                              <Building2 className="h-4 w-4" />
                              Nombre *
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Nombre del acreedor"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="nit"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="flex items-center gap-2">
                              <Hash className="h-4 w-4" />
                              NIT *
                            </FormLabel>
                            <FormControl>
                              <Input placeholder="123456789-0" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="type"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="flex items-center gap-2">
                              <FileText className="h-4 w-4" />
                              Tipo *
                            </FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              value={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Seleccione el tipo">
                                    <div className="flex items-center gap-2">
                                      {field.value && getTypeIcon(field.value)}
                                      <span>
                                        {(() => {
                                          if (field.value === 'BANK')
                                            return 'Entidad Financiera';
                                          if (field.value === 'COOPERATIVE')
                                            return 'Cooperativa';
                                          return 'Otro';
                                        })()}
                                      </span>
                                    </div>
                                  </SelectValue>
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="BANK">
                                  <div className="flex items-center gap-2">
                                    <Building className="h-4 w-4" />
                                    Entidad Financiera
                                  </div>
                                </SelectItem>
                                <SelectItem value="COOPERATIVE">
                                  <div className="flex items-center gap-2">
                                    <Users className="h-4 w-4" />
                                    Cooperativa
                                  </div>
                                </SelectItem>
                                <SelectItem value="OTHER">
                                  <div className="flex items-center gap-2">
                                    <Building2 className="h-4 w-4" />
                                    Otro
                                  </div>
                                </SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="website"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="flex items-center gap-2">
                              <Globe className="h-4 w-4" />
                              Sitio Web
                            </FormLabel>
                            <FormControl>
                              <Input placeholder="www.ejemplo.com" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-2">
                            <FileText className="h-4 w-4" />
                            Descripción
                          </FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Descripción del acreedor..."
                              className="resize-none"
                              rows={4}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="contact" className="mt-4 space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-base">
                      <Phone className="h-4 w-4" />
                      Información de Contacto
                    </CardTitle>
                    <CardDescription>
                      Datos de contacto principal del acreedor
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                      <FormField
                        control={form.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="flex items-center gap-2">
                              <Mail className="h-4 w-4" />
                              Email *
                            </FormLabel>
                            <FormControl>
                              <Input
                                type="email"
                                placeholder="<EMAIL>"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="phone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="flex items-center gap-2">
                              <Phone className="h-4 w-4" />
                              Teléfono *
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder="+57 ************"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <Separator />

                    <div className="space-y-4">
                      <h4 className="flex items-center gap-2 font-medium">
                        <MapPin className="h-4 w-4" />
                        Dirección
                      </h4>
                      <FormField
                        control={form.control}
                        name="address"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Dirección Completa *</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Calle 123 # 45-67"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <FormField
                          control={form.control}
                          name="city"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Ciudad</FormLabel>
                              <FormControl>
                                <Input placeholder="Bogotá" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="department"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Departamento</FormLabel>
                              <FormControl>
                                <Input placeholder="Cundinamarca" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="representative" className="mt-4 space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-base">
                      <User className="h-4 w-4" />
                      Representante Legal
                    </CardTitle>
                    <CardDescription>
                      Información del representante legal del acreedor
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <FormField
                      control={form.control}
                      name="representative"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-2">
                            <User className="h-4 w-4" />
                            Nombre Completo *
                          </FormLabel>
                          <FormControl>
                            <Input placeholder="Juan Pérez García" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                      <FormField
                        control={form.control}
                        name="representativeEmail"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="flex items-center gap-2">
                              <Mail className="h-4 w-4" />
                              Email del Representante
                            </FormLabel>
                            <FormControl>
                              <Input
                                type="email"
                                placeholder="<EMAIL>"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="representativePhone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="flex items-center gap-2">
                              <Phone className="h-4 w-4" />
                              Teléfono del Representante
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder="+57 ************"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="contacts" className="mt-4 space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-base">
                      <Users className="h-4 w-4" />
                      Contactos Adicionales
                    </CardTitle>
                    <CardDescription>
                      Gestione los contactos adicionales del acreedor
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-4 rounded-lg border p-4">
                      <h4 className="flex items-center gap-2 font-medium">
                        <Plus className="h-4 w-4" />
                        Agregar Nuevo Contacto
                      </h4>
                      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <div>
                          <label className="text-sm font-medium">
                            Nombre *
                          </label>
                          <Input
                            placeholder="Nombre del contacto"
                            value={newContact.name}
                            onChange={(e) =>
                              setNewContact({
                                ...newContact,
                                name: e.target.value,
                              })
                            }
                          />
                        </div>
                        <div>
                          <label className="text-sm font-medium">Cargo</label>
                          <Input
                            placeholder="Cargo o posición"
                            value={newContact.role}
                            onChange={(e) =>
                              setNewContact({
                                ...newContact,
                                role: e.target.value,
                              })
                            }
                          />
                        </div>
                        <div>
                          <label className="text-sm font-medium">Email *</label>
                          <Input
                            type="email"
                            placeholder="<EMAIL>"
                            value={newContact.email}
                            onChange={(e) =>
                              setNewContact({
                                ...newContact,
                                email: e.target.value,
                              })
                            }
                          />
                        </div>
                        <div>
                          <label className="text-sm font-medium">
                            Teléfono
                          </label>
                          <Input
                            placeholder="+57 ************"
                            value={newContact.phone}
                            onChange={(e) =>
                              setNewContact({
                                ...newContact,
                                phone: e.target.value,
                              })
                            }
                          />
                        </div>
                      </div>
                      <Button
                        type="button"
                        onClick={handleAddContact}
                        disabled={!newContact.name || !newContact.email}
                        className="w-full"
                      >
                        <Plus className="mr-2 h-4 w-4" />
                        Agregar Contacto
                      </Button>
                    </div>

                    {creditor.contacts && creditor.contacts.length > 0 && (
                      <div className="space-y-4">
                        <Separator />
                        <h4 className="font-medium">
                          Contactos Existentes ({creditor.contacts.length})
                        </h4>
                        <div className="space-y-3">
                          {creditor.contacts.map((contact) => (
                            <div
                              key={contact.id}
                              className="flex items-center justify-between rounded-lg border p-4"
                            >
                              <div className="space-y-1">
                                <div className="flex items-center gap-2">
                                  <User className="text-muted-foreground h-4 w-4" />
                                  <span className="font-medium">
                                    {contact.name}
                                  </span>
                                  {contact.role && (
                                    <Badge variant="outline">
                                      {contact.role}
                                    </Badge>
                                  )}
                                </div>
                                <div className="text-muted-foreground flex items-center gap-4 text-sm">
                                  <div className="flex items-center gap-1">
                                    <Mail className="h-3 w-3" />
                                    {contact.email}
                                  </div>
                                  {contact.phone && (
                                    <div className="flex items-center gap-1">
                                      <Phone className="h-3 w-3" />
                                      {contact.phone}
                                    </div>
                                  )}
                                </div>
                              </div>
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => handleDeleteContact(contact.id)}
                                className="text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {(!creditor.contacts || creditor.contacts.length === 0) && (
                      <div className="rounded-lg border border-dashed p-6 text-center">
                        <Users className="text-muted-foreground mx-auto h-8 w-8" />
                        <p className="text-muted-foreground mt-2 text-sm">
                          No hay contactos adicionales registrados
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>

            <DialogFooter>
              <DialogClose asChild>
                <Button ref={closeRef} variant="outline">
                  Cancelar
                </Button>
              </DialogClose>
              <Button type="submit" disabled={isPending}>
                {isPending ? 'Guardando...' : 'Guardar Cambios'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
