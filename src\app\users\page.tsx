import { <PERSON>, Users, User<PERSON>heck, Settings } from 'lucide-react';

import { Card, CardContent } from '@/components/ui/card';
import { getAllRoles } from '@/features/role/actions';
import { getAllUsers } from '@/features/user/actions';

import { DashboardHeader } from '../dashboard/components/dashboard-header';

import { UsersPageClient } from './users-page-client';

import type { UIUser, UIRole } from '@/features/user/types';

interface PageProps {
  searchParams: Promise<{
    search?: string;
    role?: string;
    tab?: string;
  }>;
}

export default async function UsersPage({ searchParams }: Readonly<PageProps>) {
  const params = await searchParams;
  const tab = params.tab ?? 'users';

  const [users, rolesData] = await Promise.all([getAllUsers(), getAllRoles()]);

  const uiUsers: UIUser[] = users.map((user) => ({
    ...user,
    lastLogin: user.lastLogin ? user.lastLogin.toISOString() : 'Nunca',
    createdDate: user.createdDate.toISOString(),
    casesAssigned: user.assignedCases?.length ?? 0,
    permissions: user.role?.permissions ?? [],
  }));

  const roles: UIRole[] = rolesData.map((role) => ({
    ...role,
    users: role._count.users,
  }));

  const activeUsersCount = uiUsers.filter((u) => u.status === 'Activo').length;
  const operatorsCount = uiUsers.filter(
    (u) => u.role.name === 'Operadora de Insolvencia',
  ).length;
  const totalCases = uiUsers.reduce((sum, u) => sum + u.casesAssigned, 0);

  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardHeader />

      <main className="container mx-auto px-4 py-6">
        <div className="space-y-6">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
            <Card>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Total Usuarios
                    </p>
                    <p className="text-2xl font-bold">{uiUsers.length}</p>
                  </div>
                  <Users className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Usuarios Activos
                    </p>
                    <p className="text-2xl font-bold">{activeUsersCount}</p>
                  </div>
                  <UserCheck className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Operadores
                    </p>
                    <p className="text-2xl font-bold">{operatorsCount}</p>
                  </div>
                  <Shield className="h-8 w-8 text-red-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Casos Asignados
                    </p>
                    <p className="text-2xl font-bold">{totalCases}</p>
                  </div>
                  <Settings className="h-8 w-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          <UsersPageClient
            initialUsers={uiUsers}
            roles={roles}
            initialTab={tab}
            searchParams={params}
          />
        </div>
      </main>
    </div>
  );
}
