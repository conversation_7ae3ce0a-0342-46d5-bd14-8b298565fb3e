'use client';

import { useState } from 'react';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { RoleCard } from './role-card';
import { RoleManagementButton } from './role-management-button';
import { UserActivityList } from './user-activity-list';
import { UsersTable } from './users-table';

import type { UIUser, UIRole } from '@/features/user/types';

interface UserTabsProps {
  initialTab?: string;
  searchParams: {
    search?: string;
    role?: string;
  };
  users: UIUser[];
  roles: UIRole[];
  onUserUpdated?: () => void;
}

export function UserTabs({
  initialTab = 'users',
  searchParams,
  users,
  roles,
  onUserUpdated,
}: Readonly<UserTabsProps>) {
  const [activeTab, setActiveTab] = useState(initialTab);

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
      <TabsList>
        <TabsTrigger value="users">Usuarios</TabsTrigger>
        <TabsTrigger value="roles">Roles y Permisos</TabsTrigger>
        <TabsTrigger value="activity">Actividad</TabsTrigger>
      </TabsList>

      <TabsContent value="users" className="space-y-4">
        <UsersTable
          searchParams={searchParams}
          users={users}
          roles={roles}
          onUserUpdated={onUserUpdated}
        />
      </TabsContent>

      <TabsContent value="roles" className="space-y-4">
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Roles y Permisos</CardTitle>
                <CardDescription>
                  Configure los roles del sistema y sus permisos asociados
                </CardDescription>
              </div>
              <RoleManagementButton onRoleCreated={onUserUpdated} />
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
              {roles.map((role) => (
                <RoleCard
                  key={role.id}
                  role={role}
                  users={users}
                  onRoleUpdated={onUserUpdated}
                />
              ))}
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="activity">
        <Card>
          <CardHeader>
            <CardTitle>Actividad de Usuarios</CardTitle>
            <CardDescription>
              Registro de actividad reciente en el sistema
            </CardDescription>
          </CardHeader>
          <CardContent>
            <UserActivityList />
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  );
}
