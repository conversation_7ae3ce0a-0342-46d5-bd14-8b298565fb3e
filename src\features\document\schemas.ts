import { z } from 'zod';

export const createDocumentSchema = z.object({
  name: z.string().min(1, 'El nombre es requerido'),
  type: z.string().min(1, 'El tipo es requerido'),
  status: z.string().optional().default('PENDIENTE'),
  url: z.string().url('URL inválida'),
  caseId: z.string().min(1, 'El ID del caso es requerido'),
});

export const updateDocumentSchema = z.object({
  id: z.string().min(1, 'El ID es requerido'),
  name: z.string().min(1, 'El nombre es requerido').optional(),
  type: z.string().min(1, 'El tipo es requerido').optional(),
  status: z.string().optional(),
  url: z.string().url('URL inválida').optional(),
  caseId: z.string().min(1, 'El ID del caso es requerido').optional(),
});
