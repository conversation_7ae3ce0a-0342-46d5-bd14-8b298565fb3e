export const PERMISSIONS = {
  'case.create': '<PERSON><PERSON>r casos',
  'case.read': 'Ver casos',
  'case.update': 'Editar casos',
  'case.delete': 'Eliminar casos',

  'document.create': 'Subir documentos',
  'document.read': 'Consultar documentos',
  'document.update': 'Editar documentos',
  'document.delete': 'Eliminar documentos',
  'document.generate': 'Generar documentos',

  'hearing.manage': 'Programar audiencias',

  'creditor.manage': 'Gestionar acreedores',

  'debtor.manage': 'Gestionar deudores',

  'report.generate': 'Generar reportes',
  'report.export': 'Exportar datos',

  'user.manage': 'Administrar usuarios',

  'calendar.manage': 'Gestionar agenda',

  'system.configure': 'Configurar sistema',
  'stats.view': 'Ver estadísticas',

  all: 'Acceso completo',
} as const;

export type PermissionKey = keyof typeof PERMISSIONS;

export const PERMISSION_NAMES_TO_KEYS = Object.entries(PERMISSIONS).reduce(
  (acc, [key, value]) => {
    acc[value] = key as PermissionKey;
    return acc;
  },
  {} as Record<string, PermissionKey>,
);

export function getPermissionName(key: string): string {
  return PERMISSIONS[key as PermissionKey] || key;
}

export function getPermissionKey(name: string): string {
  return PERMISSION_NAMES_TO_KEYS[name] || name;
}

export function getPermissionNames(keys: string[]): string[] {
  return keys.map((key) => getPermissionName(key));
}

export function getPermissionKeys(names: string[]): string[] {
  return names.map((name) => getPermissionKey(name));
}
