'use client';

import { Upload } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';

export function DocumentsSection() {
  const mandatoryDocs = [
    'Cédula de ciudadanía',
    'Certificado REDAM',
    'Certificado de ingresos',
    'Extractos bancarios',
    'Relación de acreedores',
  ];

  const optionalDocs = [
    'Certificado de tradición y libertad',
    'Constancia matrícula mercantil',
    'Poder notariado',
    'Gastos de manutención',
    'Procesos judiciales',
  ];

  return (
    <Card className="shadow-sm">
      <CardHeader>
        <CardTitle>Cargar Documentos</CardTitle>
        <CardDescription>
          Suba los documentos necesarios para el caso
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <Label>Documentos Obligatorios</Label>
            <div className="space-y-2">
              {mandatoryDocs.map((doc, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between rounded border p-2"
                >
                  <span className="text-sm">{doc}</span>
                  <Button variant="outline" size="sm">
                    <Upload className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
          <div className="space-y-2">
            <Label>Documentos Opcionales</Label>
            <div className="space-y-2">
              {optionalDocs.map((doc, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between rounded border p-2"
                >
                  <span className="text-sm">{doc}</span>
                  <Button variant="outline" size="sm">
                    <Upload className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
