'use client';

import {
  User,
  CreditCard,
  Mail,
  Phone,
  MapPin,
  Briefcase,
  DollarSign,
  AlertCircle,
  Building,
  Eye,
  Activity,
  FileText,
} from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { type DebtorWithRelations } from '@/features/debtor/types';

interface DebtorDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  debtor: DebtorWithRelations;
}

export function DebtorDetailsDialog({
  open,
  onOpen<PERSON>hange,
  debtor,
}: Readonly<DebtorDetailsDialogProps>) {
  if (!debtor) return null;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'En proceso':
        return 'bg-blue-100 text-blue-800';
      case 'Audiencia programada':
        return 'bg-green-100 text-green-800';
      case 'Documentos pendientes':
        return 'bg-orange-100 text-orange-800';
      case 'Acuerdo aprobado':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const debts = [
    {
      id: 1,
      creditor: 'Banco Nacional',
      amount: 25000000,
      type: 'Primera Clase',
      status: 'Activa',
    },
    {
      id: 2,
      creditor: 'Cooperativa del Valle',
      amount: 15000000,
      type: 'Segunda Clase',
      status: 'Activa',
    },
    {
      id: 3,
      creditor: 'Tarjetas XYZ',
      amount: 5000000,
      type: 'Quinta Clase',
      status: 'Activa',
    },
  ];

  const activityLog = [
    {
      date: '2025-01-20',
      action: 'Documentos de ingresos actualizados',
      user: 'Beatriz Helena Malavera',
    },
    {
      date: '2025-01-15',
      action: 'Audiencia programada para el 14/2/2025',
      user: 'Sistema',
    },
    {
      date: '2025-01-10',
      action: 'Caso creado en el sistema',
      user: 'Beatriz Helena Malavera',
    },
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-6xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Eye className="mr-2 h-5 w-5" />
            Detalles del Deudor
          </DialogTitle>
          <DialogDescription>
            Información completa de {debtor.name}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-4">
                  <div className="rounded-lg bg-blue-100 p-3">
                    <User className="h-8 w-8 text-blue-600" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold">{debtor.name}</h2>
                    <p className="mt-1 flex items-center text-gray-600">
                      <CreditCard className="mr-1 h-4 w-4" />
                      {debtor.idType}: {debtor.idNumber}
                    </p>
                    <div className="mt-2 flex items-center space-x-2">
                      <Badge className={getStatusColor(debtor.status)}>
                        {debtor.status}
                      </Badge>
                      <Badge variant="outline">{debtor.occupation}</Badge>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-600">Deuda Total</p>
                  <p className="text-2xl font-bold text-red-600">
                    ${debtor.totalDebt?.toLocaleString() || '0'}
                  </p>
                  <p className="mt-2 text-sm text-gray-600">Casos Activos</p>
                  <p className="text-lg font-medium text-blue-600">
                    {debtor.activeCases || 1}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Tabs defaultValue="personal" className="space-y-4">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="personal">Personal</TabsTrigger>
              <TabsTrigger value="contact">Contacto</TabsTrigger>
              <TabsTrigger value="financial">Financiero</TabsTrigger>
              <TabsTrigger value="debts">Deudas</TabsTrigger>
              <TabsTrigger value="activity">Actividad</TabsTrigger>
            </TabsList>

            <TabsContent value="personal" className="space-y-4">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <User className="mr-2 h-5 w-5" />
                      Información Personal
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Nombre Completo:</span>
                      <span className="font-medium">{debtor.name}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Documento:</span>
                      <span className="font-medium">
                        {debtor.idType}: {debtor.idNumber}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">
                        Fecha de Nacimiento:
                      </span>
                      <span className="font-medium">
                        {debtor.birthDate
                          ? new Date(debtor.birthDate).toLocaleDateString(
                              'es-CO',
                            )
                          : 'No especificado'}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Estado Civil:</span>
                      <span className="font-medium">
                        {debtor.maritalStatus ?? 'No especificado'}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Nivel Educativo:</span>
                      <span className="font-medium">
                        {debtor.educationLevel ?? 'No especificado'}
                      </span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Briefcase className="mr-2 h-5 w-5" />
                      Información Laboral
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Ocupación:</span>
                      <Badge variant="outline">{debtor.occupation}</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Ingresos Mensuales:</span>
                      <span className="font-medium">
                        ${debtor.monthlyIncome?.toLocaleString() ?? '0'}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Gastos Mensuales:</span>
                      <span className="font-medium">
                        ${debtor.monthlyExpenses?.toLocaleString() ?? '0'}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Personas a Cargo:</span>
                      <span className="font-medium">
                        {debtor.dependents ?? 0}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Capacidad de Pago:</span>
                      <span className="font-medium text-green-600">
                        $
                        {(
                          (debtor.monthlyIncome ?? 0) -
                          (debtor.monthlyExpenses ?? 0)
                        ).toLocaleString()}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="contact" className="space-y-4">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Phone className="mr-2 h-5 w-5" />
                      Información de Contacto
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Mail className="h-4 w-4 text-gray-400" />
                      <span>{debtor.email}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Phone className="h-4 w-4 text-gray-400" />
                      <span>{debtor.phone}</span>
                    </div>
                    <div className="flex items-start space-x-2">
                      <MapPin className="mt-1 h-4 w-4 text-gray-400" />
                      <div>
                        <p>{debtor.address}</p>
                        <p className="text-sm text-gray-600">
                          {debtor.city}, {debtor.department}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <AlertCircle className="mr-2 h-5 w-5" />
                      Contacto de Emergencia
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Nombre:</span>
                      <span className="font-medium">
                        {debtor.emergencyContact ?? 'No especificado'}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Teléfono:</span>
                      <span className="font-medium">
                        {debtor.emergencyPhone ?? 'No especificado'}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="financial" className="space-y-4">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <DollarSign className="mr-2 h-5 w-5" />
                      Información Financiera
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Ingresos Mensuales:</span>
                      <span className="font-bold text-green-600">
                        ${debtor.monthlyIncome?.toLocaleString() ?? '0'}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Gastos Mensuales:</span>
                      <span className="font-bold text-red-600">
                        ${debtor.monthlyExpenses?.toLocaleString() ?? '0'}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Capacidad de Pago:</span>
                      <span className="font-bold text-blue-600">
                        $
                        {(
                          (debtor.monthlyIncome ?? 0) -
                          (debtor.monthlyExpenses ?? 0)
                        ).toLocaleString()}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Deuda Total:</span>
                      <span className="text-lg font-bold">
                        ${debtor.totalDebt?.toLocaleString() ?? '0'}
                      </span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Building className="mr-2 h-5 w-5" />
                      Información Bancaria
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Banco:</span>
                      <span className="font-medium">
                        {debtor.bankName ?? 'No especificado'}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Tipo de Cuenta:</span>
                      <span className="font-medium">
                        {debtor.accountType ?? 'No especificado'}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Número de Cuenta:</span>
                      <span className="font-medium">
                        {debtor.bankAccount ?? 'No especificado'}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="debts" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <FileText className="mr-2 h-5 w-5" />
                    Deudas Registradas
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Acreedor</TableHead>
                        <TableHead>Monto</TableHead>
                        <TableHead>Tipo</TableHead>
                        <TableHead>Estado</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {debts.map((debt) => (
                        <TableRow key={debt.id}>
                          <TableCell className="font-medium">
                            {debt.creditor}
                          </TableCell>
                          <TableCell>${debt.amount.toLocaleString()}</TableCell>
                          <TableCell>
                            <Badge variant="outline">{debt.type}</Badge>
                          </TableCell>
                          <TableCell>
                            <Badge className="bg-green-100 text-green-800">
                              {debt.status}
                            </Badge>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="activity" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Activity className="mr-2 h-5 w-5" />
                    Registro de Actividad
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {activityLog.map((activity, index) => (
                      <div
                        key={index}
                        className="flex items-start space-x-3 border-l-2 border-blue-200 p-3"
                      >
                        <div className="flex-1">
                          <p className="font-medium">{activity.action}</p>
                          <p className="text-sm text-gray-600">
                            Por: {activity.user}
                          </p>
                        </div>
                        <div className="text-sm text-gray-500">
                          {new Date(activity.date).toLocaleDateString('es-CO')}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        <div className="flex justify-end pt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cerrar
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
