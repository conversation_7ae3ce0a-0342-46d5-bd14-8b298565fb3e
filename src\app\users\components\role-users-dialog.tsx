'use client';

import { Users, Mail, Phone, Calendar, User<PERSON><PERSON><PERSON>, Clock } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { getPermissionName } from '@/lib/permissions';

import type { UIUser, UIRole } from '@/features/user/types';

interface RoleUsersDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  role: UIRole | null;
  users: UIUser[];
}

export function RoleUsersDialog({
  open,
  onOpenChange,
  role,
  users,
}: Readonly<RoleUsersDialogProps>) {
  if (!role) return null;

  const roleUsers = users.filter((user) => user?.role?.name === role?.name);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Activo':
        return 'bg-green-100 text-green-800';
      case 'Inactivo':
        return 'bg-red-100 text-red-800';
      case 'Suspendido':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatLastLogin = (lastLogin: string) => {
    if (lastLogin === 'Nunca') return 'Nunca';
    return new Date(lastLogin).toLocaleString('es-CO', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-7xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Usuarios con rol: {role?.name ?? 'N/A'}
          </DialogTitle>
          <DialogDescription>
            Lista de usuarios asignados al rol &quot;{role?.name ?? 'N/A'}&quot;
            ({roleUsers.length} usuarios)
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          <Card className="shadow-sm">
            <CardHeader>
              <CardTitle className="text-lg">Información del Rol</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Nombre del Rol
                  </p>
                  <p className="text-lg font-semibold">{role?.name ?? 'N/A'}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Descripción
                  </p>
                  <p className="text-sm text-gray-800">
                    {role?.description ?? 'N/A'}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Usuarios Asignados
                  </p>
                  <div className="flex items-center gap-2">
                    <Badge className={role?.color ?? ''}>
                      {roleUsers.length}
                    </Badge>
                    <span className="text-sm text-gray-600">usuarios</span>
                  </div>
                </div>
              </div>

              <div className="mt-4">
                <p className="mb-2 text-sm font-medium text-gray-600">
                  Permisos del Rol
                </p>
                <div className="flex flex-wrap gap-2">
                  {(role?.permissions ?? []).map((permission, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {getPermissionName(permission)}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {roleUsers.length > 0 ? (
            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle>Usuarios Asignados</CardTitle>
                <CardDescription>
                  Detalles de los usuarios que tienen el rol &quot;{role.name}
                  &quot;
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Usuario</TableHead>
                        <TableHead>Estado</TableHead>
                        <TableHead>Casos Asignados</TableHead>
                        <TableHead>Último Acceso</TableHead>
                        <TableHead>Tarjeta Profesional</TableHead>
                        <TableHead>Contacto</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {roleUsers.map((user) => (
                        <TableRow key={user.id}>
                          <TableCell>
                            <div className="space-y-1">
                              <p className="font-medium">{user.name}</p>
                              <div className="flex items-center text-sm text-gray-600">
                                <Mail className="mr-1 h-3 w-3" />
                                {user.email}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge className={getStatusColor(user.status)}>
                              {user.status}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <UserCheck className="mr-2 h-4 w-4 text-blue-600" />
                              {user?.assignedCases?.length ?? 0}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center text-sm">
                              <Clock className="mr-1 h-3 w-3 text-gray-400" />
                              {formatLastLogin(user?.lastLogin ?? 'Nunca')}
                            </div>
                          </TableCell>
                          <TableCell>
                            <span className="font-mono text-sm">
                              {user.professionalCard}
                            </span>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1 text-sm">
                              <div className="flex items-center">
                                <Phone className="mr-1 h-3 w-3 text-gray-400" />
                                {user.phone}
                              </div>
                              <div className="flex items-center">
                                <Calendar className="mr-1 h-3 w-3 text-gray-400" />
                                Desde{' '}
                                {new Date(user.createdDate).toLocaleDateString(
                                  'es-CO',
                                )}
                              </div>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card className="shadow-sm">
              <CardContent className="py-8 text-center">
                <Users className="mx-auto mb-4 h-12 w-12 text-gray-400" />
                <h3 className="mb-2 text-lg font-medium text-gray-900">
                  No hay usuarios asignados
                </h3>
                <p className="text-gray-600">
                  Actualmente no hay usuarios con el rol &quot;{role.name}&quot;
                </p>
              </CardContent>
            </Card>
          )}

          {roleUsers.length > 0 && (
            <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
              <Card className="shadow-sm">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Usuarios Activos
                      </p>
                      <p className="text-2xl font-bold text-green-600">
                        {roleUsers.filter((u) => u.status === 'Activo').length}
                      </p>
                    </div>
                    <UserCheck className="h-8 w-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-sm">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Total Casos
                      </p>
                      <p className="text-2xl font-bold text-blue-600">
                        {roleUsers.reduce(
                          (sum, u) => sum + (u?.assignedCases?.length ?? 0),
                          0,
                        )}
                      </p>
                    </div>
                    <Users className="h-8 w-8 text-blue-600" />
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-sm">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Promedio Casos
                      </p>
                      <p className="text-2xl font-bold text-purple-600">
                        {Math.round(
                          roleUsers.reduce(
                            (sum, u) => sum + (u?.assignedCases?.length ?? 0),
                            0,
                          ) / roleUsers.length,
                        )}
                      </p>
                    </div>
                    <Calendar className="h-8 w-8 text-purple-600" />
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-sm">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Con Tarjeta Prof.
                      </p>
                      <p className="text-2xl font-bold text-orange-600">
                        {
                          roleUsers.filter((u) => u.professionalCard !== 'N/A')
                            .length
                        }
                      </p>
                    </div>
                    <Badge className="h-8 w-8 text-orange-600" />
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cerrar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
