'use client';

import { useRouter } from 'next/navigation';

import { UserTabs } from './user-tabs';

import type { UIUser, UIRole } from '@/features/user/types';

interface UsersPageClientProps {
  initialUsers: UIUser[];
  roles: UIRole[];
  initialTab: string;
  searchParams: {
    search?: string;
    role?: string;
  };
}

export function UsersPageClient({
  initialUsers,
  roles,
  initialTab,
  searchParams,
}: Readonly<UsersPageClientProps>) {
  const router = useRouter();

  const handleUserUpdated = () => {
    router.refresh();
  };

  return (
    <>
      <div>
        <h1 className="text-3xl font-bold text-gray-900">
          Gestión de usuarios y permisos
        </h1>
        <p className="text-gray-600">
          Administre usuarios y permisos del sistema
        </p>
      </div>

      <UserTabs
        initialTab={initialTab}
        searchParams={searchParams}
        users={initialUsers}
        roles={roles}
        onUserUpdated={handleUserUpdated}
      />
    </>
  );
}
