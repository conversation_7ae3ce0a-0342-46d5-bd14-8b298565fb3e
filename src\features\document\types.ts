import type { createDocumentSchema, updateDocumentSchema } from './schemas';
import type { Prisma } from '@prisma/client';
import type { z } from 'zod';

export type CreateDocumentData = z.infer<typeof createDocumentSchema>;
export type UpdateDocumentData = z.infer<typeof updateDocumentSchema>;

export interface DocumentFilter {
  caseId?: string;
  type?: string;
  status?: string;
  search?: string;
}

export interface Document {
  id: string;
  name: string;
  type: string;
  status: string;
  uploadDate?: string;
  url?: string;
  caseId?: string;
  debtorName?: string;
  category?: string;
  createdDate?: string;
  size?: string;
  format?: string;
  createdBy?: string;
  downloadCount?: number;
  lastAccessed?: string;
  content?: string;
  viewCount?: number;
  shareCount?: number;
}

export type DocumentWithCase = Prisma.DocumentGetPayload<{
  include: {
    case: {
      select: {
        id: true;
        caseNumber: true;
        debtorName: true;
        type: true;
        status: true;
      };
    };
  };
}>;
