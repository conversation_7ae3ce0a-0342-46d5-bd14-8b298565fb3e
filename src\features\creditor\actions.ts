'use server';

import { revalidatePath } from 'next/cache';
import { createSafeActionClient } from 'next-safe-action';

import prisma from '@/lib/prisma';

import {
  createCreditorSchema,
  updateCreditorSchema,
  deleteCreditorSchema,
  addContactSchema,
  removeContactSchema,
  toggleCreditorStatusSchema,
} from './schemas';

import type { Prisma } from '@prisma/client';

const actionClient = createSafeActionClient();

const convertDecimalToNumber = (
  decimal: Prisma.Decimal | null | undefined,
): number => {
  if (!decimal) return 0;
  return parseFloat(decimal.toString());
};

export async function getCreditors() {
  try {
    const creditors = await prisma.creditor.findMany({
      include: {
        contacts: true,
        debts: true,
        _count: {
          select: {
            debts: true,
          },
        },
      },
      orderBy: {
        name: 'asc',
      },
    });

    return creditors.map((creditor) => ({
      id: creditor.id,
      name: creditor.name,
      type: creditor.type,
      email: creditor.email,
      phone: creditor.phone,
      address: creditor.address,
      status: creditor.status,
      representative: creditor.representative,
      nit: creditor.nit,
      website: creditor.website,
      city: creditor.city,
      department: creditor.department,
      bankName: creditor.bankName,
      activeCases: creditor.activeCases,
      createdDate: creditor.createdDate,
      lastUpdate: creditor.lastUpdate,
      description: creditor.description,
      representativeId: creditor.representativeId,
      representativeEmail: creditor.representativeEmail,
      representativePhone: creditor.representativePhone,
      contacts: creditor.contacts,
      debts: creditor.debts.map((debt) => ({
        id: debt.id,
        amount: convertDecimalToNumber(debt.amount),
        interestRate: debt.interestRate,
        type: debt.type,
        caseId: debt.caseId,
        creditorId: debt.creditorId,
        debtorId: debt.debtorId,
      })),
      _count: creditor._count,
    }));
  } catch (error) {
    console.error('Error fetching creditors:', error);
    throw new Error('Error al obtener los acreedores');
  }
}

export async function getCreditorById(id: string) {
  try {
    if (!id) {
      throw new Error('ID del acreedor es requerido');
    }

    const creditor = await prisma.creditor.findUnique({
      where: { id },
      include: {
        contacts: true,
        debts: {
          include: {
            case: {
              include: {
                debtor: true,
              },
            },
          },
        },
        _count: {
          select: {
            debts: true,
          },
        },
      },
    });

    if (!creditor) {
      return null;
    }

    return {
      id: creditor.id,
      name: creditor.name,
      type: creditor.type,
      email: creditor.email,
      phone: creditor.phone,
      address: creditor.address,
      status: creditor.status,
      representative: creditor.representative,
      nit: creditor.nit,
      website: creditor.website,
      city: creditor.city,
      department: creditor.department,
      bankName: creditor.bankName,
      activeCases: creditor.activeCases,
      createdDate: creditor.createdDate,
      lastUpdate: creditor.lastUpdate,
      description: creditor.description,
      representativeId: creditor.representativeId,
      representativeEmail: creditor.representativeEmail,
      representativePhone: creditor.representativePhone,
      contacts: creditor.contacts,
      debts: creditor.debts.map((debt) => ({
        id: debt.id,
        amount: convertDecimalToNumber(debt.amount),
        interestRate: debt.interestRate,
        type: debt.type,
        caseId: debt.caseId,
        creditorId: debt.creditorId,
        debtorId: debt.debtorId,
        case: debt.case,
      })),
      _count: creditor._count,
    };
  } catch (error) {
    console.error('Error fetching creditor:', error);
    throw new Error('Error al obtener el acreedor');
  }
}

export const createCreditor = actionClient
  .inputSchema(createCreditorSchema)
  .action(async ({ parsedInput: data }) => {
    try {
      const creditor = await prisma.creditor.create({
        data: {
          ...data,
          status: 'Activo',
        },
        include: {
          contacts: true,
          debts: true,
          _count: {
            select: {
              debts: true,
            },
          },
        },
      });

      revalidatePath('/creditors');

      const serializedCreditor = {
        ...creditor,
        debts: creditor.debts.map((debt) => ({
          ...debt,
          amount: convertDecimalToNumber(debt.amount),
        })),
      };

      return {
        success: true,
        message: 'Acreedor creado exitosamente',
        data: serializedCreditor,
      };
    } catch (error) {
      console.error('Error creating creditor:', error);

      if (error instanceof Error && 'code' in error && error.code === 'P2002') {
        const prismaError = error as { meta?: { target?: string[] } };
        if (prismaError.meta?.target?.includes('nit')) {
          throw new Error(
            'Ya existe un acreedor con este NIT. Por favor verifique el número.',
          );
        }
        if (prismaError.meta?.target?.includes('email')) {
          throw new Error(
            'Ya existe un acreedor con este email. Por favor use otro email.',
          );
        }
      }

      throw new Error('Error al crear el acreedor');
    }
  });

export const updateCreditor = actionClient
  .inputSchema(updateCreditorSchema)
  .action(async ({ parsedInput: data }) => {
    try {
      const updatedCreditor = await prisma.creditor.update({
        where: { id: data.id },
        data,
        include: {
          contacts: true,
          debts: true,
          _count: {
            select: {
              debts: true,
            },
          },
        },
      });

      revalidatePath('/creditors');

      const serializedCreditor = {
        ...updatedCreditor,
        debts: updatedCreditor.debts.map((debt) => ({
          ...debt,
          amount: convertDecimalToNumber(debt.amount),
        })),
      };

      return {
        success: true,
        message: 'Acreedor actualizado exitosamente',
        data: serializedCreditor,
      };
    } catch (error) {
      console.error('Error updating creditor:', error);

      if (error instanceof Error && 'code' in error && error.code === 'P2002') {
        const prismaError = error as { meta?: { target?: string[] } };
        if (prismaError.meta?.target?.includes('nit')) {
          throw new Error(
            'Ya existe un acreedor con este NIT. Por favor verifique el número.',
          );
        }
        if (prismaError.meta?.target?.includes('email')) {
          throw new Error(
            'Ya existe un acreedor con este email. Por favor use otro email.',
          );
        }
      }

      throw new Error('Error al actualizar el acreedor');
    }
  });

export const deleteCreditor = actionClient
  .inputSchema(deleteCreditorSchema)
  .action(async ({ parsedInput: { id } }) => {
    try {
      const deletedCreditor = await prisma.creditor.delete({
        where: { id },
      });

      revalidatePath('/creditors');

      return {
        success: true,
        message: 'Acreedor eliminado exitosamente',
        data: deletedCreditor,
      };
    } catch (error) {
      console.error('Error deleting creditor:', error);
      throw new Error('Error al eliminar el acreedor');
    }
  });

export const addContact = actionClient
  .inputSchema(addContactSchema)
  .action(async ({ parsedInput: { creditorId, name, email, phone, role } }) => {
    try {
      const contact = await prisma.contact.create({
        data: {
          name,
          email,
          phone,
          role,
          creditorId,
        },
      });

      revalidatePath('/creditors');

      return {
        success: true,
        message: 'Contacto agregado exitosamente',
        data: contact,
      };
    } catch (error) {
      console.error('Error adding contact:', error);
      throw new Error('Error al agregar el contacto');
    }
  });

export const removeContact = actionClient
  .inputSchema(removeContactSchema)
  .action(async ({ parsedInput: { contactId } }) => {
    try {
      await prisma.contact.delete({
        where: { id: contactId },
      });

      revalidatePath('/creditors');

      return {
        success: true,
        message: 'Contacto eliminado exitosamente',
      };
    } catch (error) {
      console.error('Error removing contact:', error);
      throw new Error('Error al eliminar el contacto');
    }
  });

export const toggleCreditorStatus = actionClient
  .inputSchema(toggleCreditorStatusSchema)
  .action(async ({ parsedInput: { id } }) => {
    try {
      const creditor = await prisma.creditor.findUnique({
        where: { id },
        select: { status: true },
      });

      if (!creditor) {
        throw new Error('Acreedor no encontrado');
      }

      const newStatus = creditor.status === 'Activo' ? 'Inactivo' : 'Activo';

      await prisma.creditor.update({
        where: { id },
        data: { status: newStatus },
      });

      revalidatePath('/creditors');

      return {
        success: true,
        message: `Estado del acreedor actualizado a ${newStatus}`,
      };
    } catch (error) {
      console.error('Error toggling creditor status:', error);
      throw new Error('Error al cambiar el estado del acreedor');
    }
  });
