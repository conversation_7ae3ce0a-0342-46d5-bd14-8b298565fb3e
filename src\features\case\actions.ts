'use server';

import { revalidatePath } from 'next/cache';

import prisma from '@/lib/prisma';

import { createCaseSchema, updateCaseSchema } from './schemas';

import type { CreateCaseData, UpdateCaseData, CaseActionResult } from './types';
import type { ActionResult } from '@/lib/types';
import type { Prisma } from '@prisma/client';

const convertDecimalToNumber = (
  decimal: Prisma.Decimal | null | undefined,
): number => {
  if (!decimal) return 0;
  return parseFloat(decimal.toString());
};

const mapCaseTypeToSpanish = (type: string): string => {
  const typeMap: Record<string, string> = {
    INSOLVENCY: 'Insolvencia',
    CONCILIATION: 'Conciliación',
    SUPPORT_AGREEMENT: 'Acuerdo de Apoyo',
  };
  return typeMap[type] || type;
};

const mapCaseStatusToSpanish = (status: string): string => {
  const statusMap: Record<string, string> = {
    NEGOTIATION: 'En negociación',
    HEARING_SCHEDULED: 'Audiencia programada',
    PENDING_DOCUMENTS: 'Documentos pendientes',
    AGREEMENT_APPROVED: 'Acuerdo aprobado',
    CLOSED: 'Cerrado',
  };
  return statusMap[status] || status;
};

export async function getCases() {
  try {
    const cases = await prisma.case.findMany({
      include: {
        debtor: {
          select: {
            id: true,
            name: true,
            idNumber: true,
            email: true,
            phone: true,
          },
        },
        operator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        _count: {
          select: {
            documents: true,
          },
        },
      },
      orderBy: {
        createdDate: 'desc',
      },
    });

    return cases.map((caseItem) => ({
      id: caseItem.id,
      caseNumber: caseItem.caseNumber,
      debtorName: caseItem.debtorName,
      type: mapCaseTypeToSpanish(caseItem.type),
      status: mapCaseStatusToSpanish(caseItem.status),
      totalDebt: convertDecimalToNumber(caseItem.totalDebt),
      creditors: caseItem.creditors,
      createdDate: caseItem.createdDate,
      hearingDate: caseItem.hearingDate,
      phase: caseItem.phase,
      causes: caseItem.causes || [],
      debtorId: caseItem.debtorId,
      operatorId: caseItem.operatorId,
      debtor: caseItem.debtor,
      operator: caseItem.operator,
      _count: caseItem._count,
    }));
  } catch {
    throw new Error('Error al obtener los casos');
  }
}

export async function getCaseById(id: string) {
  try {
    if (!id) {
      throw new Error('ID del caso es requerido');
    }

    const caseItem = await prisma.case.findUnique({
      where: { id },
      include: {
        debtor: true,
        operator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        documents: true,
        debts: {
          include: {
            creditor: {
              select: {
                name: true,
              },
            },
          },
        },
        assets: true,
      },
    });

    if (!caseItem) {
      return null;
    }

    return {
      id: caseItem.id,
      caseNumber: caseItem.caseNumber,
      debtorName: caseItem.debtorName,
      type: mapCaseTypeToSpanish(caseItem.type),
      status: mapCaseStatusToSpanish(caseItem.status),
      totalDebt: convertDecimalToNumber(caseItem.totalDebt),
      creditors: caseItem.creditors,
      createdDate: caseItem.createdDate,
      hearingDate: caseItem.hearingDate,
      phase: caseItem.phase,
      causes: caseItem.causes || [],
      debtorId: caseItem.debtorId,
      operatorId: caseItem.operatorId,
      operator: caseItem.operator,
      documents: caseItem.documents,
      debtor: {
        id: caseItem.debtor.id,
        name: caseItem.debtor.name,
        idNumber: caseItem.debtor.idNumber,
        idType: caseItem.debtor.idType,
        email: caseItem.debtor.email,
        phone: caseItem.debtor.phone,
        address: caseItem.debtor.address,
        city: caseItem.debtor.city,
        department: caseItem.debtor.department,
        birthDate: caseItem.debtor.birthDate,
        maritalStatus: caseItem.debtor.maritalStatus,
        occupation: caseItem.debtor.occupation,
        monthlyIncome: convertDecimalToNumber(caseItem.debtor.monthlyIncome),
        monthlyExpenses: convertDecimalToNumber(
          caseItem.debtor.monthlyExpenses,
        ),
        dependents: caseItem.debtor.dependents,
        educationLevel: caseItem.debtor.educationLevel,
        totalDebt: convertDecimalToNumber(caseItem.debtor.totalDebt),
        status: caseItem.debtor.status,
        emergencyContact: caseItem.debtor.emergencyContact,
        emergencyPhone: caseItem.debtor.emergencyPhone,
        bankAccount: caseItem.debtor.bankAccount,
        bankName: caseItem.debtor.bankName,
        accountType: caseItem.debtor.accountType,
        description: caseItem.debtor.description,
        activeCases: caseItem.debtor.activeCases,
        createdDate: caseItem.debtor.createdDate,
        lastUpdate: caseItem.debtor.lastUpdate,
      },
      debts: caseItem.debts.map((debt) => ({
        id: debt.id,
        amount: convertDecimalToNumber(debt.amount),
        interestRate: debt.interestRate,
        type: debt.type,
        caseId: debt.caseId,
        creditorId: debt.creditorId,
        debtorId: debt.debtorId,
        creditor: debt.creditor,
      })),
      assets: caseItem.assets.map((asset) => ({
        id: asset.id,
        name: asset.name,
        type: asset.type,
        value: convertDecimalToNumber(asset.value),
        caseId: asset.caseId,
        debtorId: asset.debtorId,
      })),
    };
  } catch (error) {
    console.error('Error fetching case:', error);
    throw new Error('Error al obtener el caso');
  }
}

export async function createCaseAction(
  values: CreateCaseData,
): Promise<ActionResult<CaseActionResult>> {
  const { success, data, error } = createCaseSchema.safeParse(values);

  if (!success) {
    return {
      success: false,
      message: 'Por favor complete todos los campos requeridos',
      errors: error.flatten().fieldErrors,
    };
  }

  try {
    let caseNumber = data.caseNumber;
    if (!caseNumber) {
      const lastCase = await prisma.case.findFirst({
        orderBy: { createdDate: 'desc' },
        select: { caseNumber: true },
      });

      const lastNumber = lastCase?.caseNumber
        ? parseInt(lastCase.caseNumber.split('-')[1]) || 0
        : 0;
      caseNumber = `CASO-${(lastNumber + 1).toString().padStart(6, '0')}`;
    }

    const debtor = await prisma.debtor.findUnique({
      where: { id: data.debtorId },
      select: { name: true },
    });

    if (!debtor) {
      throw new Error('Deudor no encontrado');
    }

    const newCase = await prisma.case.create({
      data: {
        ...data,
        caseNumber,
        debtorName: debtor.name,
        totalDebt: data.totalDebt,
        status: data.status ?? 'NEGOTIATION',
        phase: data.phase ?? 'Inicial',
        hearingDate: data.hearingDate ? new Date(data.hearingDate) : null,
      },
    });

    revalidatePath('/cases');

    return {
      success: true,
      message: 'Caso creado exitosamente',
      data: {
        ...newCase,
        totalDebt: convertDecimalToNumber(newCase.totalDebt),
      },
    };
  } catch (error) {
    console.error('Error creating case:', error);
    return {
      success: false,
      message: 'Error al crear el caso',
    };
  }
}

export async function updateCase(
  values: UpdateCaseData,
): Promise<ActionResult<CaseActionResult>> {
  const { success, data, error } = updateCaseSchema.safeParse(values);

  if (!success) {
    return {
      success: false,
      message: 'Por favor complete todos los campos requeridos',
      errors: error.flatten().fieldErrors,
    };
  }

  try {
    const updatedCase = await prisma.case.update({
      where: { id: data.id },
      data: {
        ...data,
        totalDebt: data.totalDebt,
        hearingDate: data.hearingDate ? new Date(data.hearingDate) : undefined,
      },
    });

    revalidatePath('/cases');

    return {
      success: true,
      message: 'Caso actualizado exitosamente',
      data: {
        ...updatedCase,
        totalDebt: convertDecimalToNumber(updatedCase.totalDebt),
      },
    };
  } catch (error) {
    console.error('Error updating case:', error);
    return {
      success: false,
      message: 'Error al actualizar el caso',
    };
  }
}

export async function deleteCase(
  id: string,
): Promise<ActionResult<CaseActionResult>> {
  if (!id) {
    return {
      success: false,
      message: 'ID del caso es requerido',
    };
  }

  try {
    const deletedCase = await prisma.case.delete({
      where: { id },
    });

    revalidatePath('/cases');

    return {
      success: true,
      message: 'Caso eliminado exitosamente',
      data: {
        ...deletedCase,
        totalDebt: convertDecimalToNumber(deletedCase.totalDebt),
      },
    };
  } catch (error) {
    console.error('Error deleting case:', error);
    return {
      success: false,
      message: 'Error al eliminar el caso',
    };
  }
}

export async function getCaseStats() {
  try {
    const [total, byStatus, byType, totalDebtResult] = await Promise.all([
      prisma.case.count(),
      prisma.case.groupBy({
        by: ['status'],
        _count: {
          id: true,
        },
      }),
      prisma.case.groupBy({
        by: ['type'],
        _count: {
          id: true,
        },
      }),
      prisma.case.aggregate({
        _sum: {
          totalDebt: true,
        },
      }),
    ]);

    const negotiation = await prisma.case.count({
      where: { status: 'NEGOTIATION' },
    });

    const agreementApproved = await prisma.case.count({
      where: { status: 'AGREEMENT_APPROVED' },
    });

    const totalDebt = convertDecimalToNumber(totalDebtResult._sum.totalDebt);

    return {
      total,
      byStatus,
      byType,
      negotiation,
      agreementApproved,
      totalDebt,
    };
  } catch (error) {
    console.error('Error fetching case stats:', error);
    throw new Error('Error al obtener las estadísticas de casos');
  }
}
